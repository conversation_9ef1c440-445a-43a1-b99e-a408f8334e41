/**
 * FILE: VPNInterfaceManager.kt
 *
 * DESCRIPTION:
 *     Unified Android VPN interface management implementation.
 *     Integrates configuration building, lifecycle management, and network settings.
 *     Equivalent to iOS NEPacketTunnelProvider with Android VpnService framework.
 *     Simplified architecture following iOS/Windows single-manager pattern.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create, 11/07/2025 refactored for unified architecture
 */

package com.panabit.client.platform

import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError

import android.content.Context
import android.net.VpnService
import android.net.IpPrefix
import android.os.Build
import android.os.ParcelFileDescriptor
import android.util.Log
import com.panabit.client.platform.models.VPNConfig
import com.panabit.client.platform.models.RouteInfo
import com.panabit.client.platform.models.RoutingMode
import com.panabit.client.network.NetworkUtils
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.IOException
import java.net.InetAddress

/**
 * NAME: VPNInterfaceState
 *
 * DESCRIPTION:
 *     VPN interface state enumeration for lifecycle management.
 *     Represents different states of VPN interface during its lifecycle.
 */
enum class VPNInterfaceState {
    INACTIVE,    // Interface is not created or has been destroyed
    CREATING,    // Interface creation is in progress
    ACTIVE,      // Interface is active and operational
    REBUILDING,  // Interface is being rebuilt due to configuration change
    DESTROYING,  // Interface destruction is in progress
    ERROR        // Interface is in error state
}

/**
 * NAME: VPNInterfaceManager
 *
 * DESCRIPTION:
 *     Unified VPN interface manager for Android platform following iOS NEPacketTunnelProvider pattern.
 *     Integrates configuration building, lifecycle management, and network settings in single manager.
 *     Handles VPN interface establishment, configuration management, and lifecycle control.
 *     Equivalent to iOS NetworkExtension with Android-specific adaptations.
 *
 * PROPERTIES:
 *     vpnService - Android VpnService instance for VPN operations
 *     currentInterface - Current VPN interface file descriptor
 *     currentConfig - Current VPN configuration
 *     interfaceState - Current interface lifecycle state
 */
class VPNInterfaceManager(private val vpnService: VpnService) {

    companion object {
        private const val TAG = "VPNInterfaceManager"

        // Default network configurations
        private const val DEFAULT_MTU = 1400
        private const val DEFAULT_LOCAL_IP = "********"
        private const val DEFAULT_PREFIX_LENGTH = 32
        private const val DEFAULT_SERVER_PORT = 443

        // Default DNS servers (Google Public DNS)
        private val DEFAULT_DNS_SERVERS = listOf("*******", "*******")

        // Local network exclusions (equivalent to iOS local network protection)
        private val LOCAL_NETWORK_EXCLUSIONS = listOf(
            RouteInfo("***********", 16),  // Link-local
            RouteInfo("*********", 4),     // Multicast
            RouteInfo("*********", 8)      // Loopback
        )

        // Common route configurations
        private val GLOBAL_ROUTES = listOf(RouteInfo("0.0.0.0", 0))

        // Lifecycle operation timeouts
        private const val CREATE_TIMEOUT_MS = 30000L
        private const val DESTROY_TIMEOUT_MS = 10000L
        private const val REBUILD_TIMEOUT_MS = 45000L
        private const val RETRY_DELAY_MS = 2000L
        private const val MAX_RETRY_ATTEMPTS = 3
    }

    // Interface state management (thread-safe)
    @Volatile
    private var currentInterface: ParcelFileDescriptor? = null
    @Volatile
    private var currentConfig: VPNConfig? = null

    // App disallowed list tracking for socket protection optimization
    @Volatile
    private var isCurrentAppDisallowed: Boolean = false

    // Lifecycle state management
    private val _interfaceState = MutableStateFlow(VPNInterfaceState.INACTIVE)
    val interfaceState: StateFlow<VPNInterfaceState> = _interfaceState.asStateFlow()

    // Lifecycle management scope
    private val lifecycleScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // MARK: - Configuration Building (integrated from VPNConfigBuilder)

    /**
     * NAME: createConfigBuilder
     *
     * DESCRIPTION:
     *     Creates a new configuration builder with default settings.
     *     Provides fluent API for constructing VPN configurations.
     *
     * RETURNS:
     *     ConfigBuilder - Builder instance for method chaining
     */
    fun createConfigBuilder(): ConfigBuilder {
        return ConfigBuilder()
    }

    /**
     * NAME: ConfigBuilder
     *
     * DESCRIPTION:
     *     Integrated configuration builder following iOS NEPacketTunnelNetworkSettings pattern.
     *     Provides fluent API for constructing VPN configurations with validation.
     */
    inner class ConfigBuilder {
        private var localIP: String = DEFAULT_LOCAL_IP
        private var prefixLength: Int = DEFAULT_PREFIX_LENGTH
        private var mtu: Int = DEFAULT_MTU
        private var routes: MutableList<RouteInfo> = mutableListOf()
        private var dnsServers: MutableList<String> = DEFAULT_DNS_SERVERS.toMutableList()
        private var excludedRoutes: MutableList<RouteInfo> = mutableListOf()
        private var serverAddress: String = ""
        private var serverPort: Int = DEFAULT_SERVER_PORT
        private var routingMode: RoutingMode = RoutingMode.ALL

        fun setLocalIP(ip: String): ConfigBuilder {
            this.localIP = ip
            logDebug("Set local IP: $ip")
            return this
        }

        fun setPrefixLength(length: Int): ConfigBuilder {
            require(length in 1..32) { "Prefix length must be between 1 and 32, got: $length" }
            this.prefixLength = length
            logDebug("Set prefix length: $length")
            return this
        }

        fun setMTU(mtu: Int): ConfigBuilder {
            require(mtu in 576..9000) { "MTU must be between 576 and 9000, got: $mtu" }
            this.mtu = mtu
            logDebug("Set MTU: $mtu")
            return this
        }

        fun setServerAddress(address: String): ConfigBuilder {
            this.serverAddress = address
            logDebug("Set server address: $address")
            return this
        }

        fun setServerPort(port: Int): ConfigBuilder {
            require(port in 1..65535) { "Server port must be between 1 and 65535, got: $port" }
            this.serverPort = port
            logDebug("Set server port: $port")
            return this
        }

        fun setRoutingMode(mode: RoutingMode): ConfigBuilder {
            this.routingMode = mode
            logDebug("Set routing mode: ${mode.toConfigValue()}")
            return this
        }

        fun addRoute(destination: String, prefixLength: Int): ConfigBuilder {
            val route = RouteInfo(destination, prefixLength)
            if (route.isValid()) {
                routes.add(route)
                logDebug("Added route: ${route.toCIDR()}")
            } else {
                logWarn("Invalid route ignored: $destination/$prefixLength")
            }
            return this
        }

        fun setDNSServers(servers: List<String>): ConfigBuilder {
            this.dnsServers.clear()
            servers.forEach { server ->
                if (isValidIPAddress(server)) {
                    this.dnsServers.add(server)
                    logDebug("Added DNS server: $server")
                } else {
                    logWarn("Invalid DNS server ignored: $server")
                }
            }
            return this
        }

        /**
         * NAME: isValidIPAddress
         *
         * DESCRIPTION:
         *     Validates IPv4 address format.
         *
         * PARAMETERS:
         *     ip - IP address string to validate
         *
         * RETURNS:
         *     Boolean - true if valid IPv4 address, false otherwise
         */
        private fun isValidIPAddress(ip: String): Boolean {
            val ipPattern = Regex(
                "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
            )
            return ipPattern.matches(ip)
        }

        fun useGlobalRouting(): ConfigBuilder {
            this.routingMode = RoutingMode.ALL
            this.routes.clear()
            this.routes.addAll(GLOBAL_ROUTES)
            logDebug("Configured for global routing")
            return this
        }

        fun addLocalNetworkExclusions(): ConfigBuilder {
            excludedRoutes.addAll(LOCAL_NETWORK_EXCLUSIONS)
            logDebug("Added local network exclusions")
            return this
        }

        fun addDynamicLocalSubnetExclusion(context: Context): ConfigBuilder {
            try {
                // Get current device's local subnet information
                val subnetInfo = NetworkUtils.getLocalSubnetInfo(context)
                if (subnetInfo != null) {
                    val (networkAddress, prefixLength) = subnetInfo
                    val localSubnetRoute = RouteInfo(networkAddress, prefixLength)

                    // Check if this subnet is not already excluded
                    val isAlreadyExcluded = excludedRoutes.any { route ->
                        route.destination == networkAddress && route.prefixLength == prefixLength
                    }

                    if (!isAlreadyExcluded) {
                        excludedRoutes.add(localSubnetRoute)
                        logInfo("Added dynamic local subnet exclusion", mapOf(
                            "network_address" to networkAddress,
                            "prefix_length" to prefixLength,
                            "subnet_cidr" to localSubnetRoute.toCIDR()
                        ))
                    } else {
                        logDebug("Local subnet already excluded", mapOf(
                            "subnet_cidr" to localSubnetRoute.toCIDR()
                        ))
                    }
                } else {
                    logWarn("Could not determine local subnet for exclusion")
                }
            } catch (e: Exception) {
                logError("Failed to add dynamic local subnet exclusion", mapOf("error" to (e.message ?: "unknown")))
            }
            return this
        }

        fun addServerExclusions(serverIPs: List<String>): ConfigBuilder {
            serverIPs.forEach { ip ->
                try {
                    // Validate IP address format
                    val inetAddress = java.net.InetAddress.getByName(ip)
                    if (inetAddress is java.net.Inet4Address) {
                        val route = RouteInfo(ip, 32) // /32 for single host exclusion
                        excludedRoutes.add(route)
                        logDebug("Added server IP exclusion: $ip")
                    } else {
                        logWarn("Skipping non-IPv4 server address: $ip")
                    }
                } catch (e: Exception) {
                    logWarn("Invalid server IP address ignored: $ip - ${e.message}")
                }
            }
            logInfo("Added server IP exclusions", mapOf("count" to serverIPs.size))
            return this
        }

        fun build(): Result<VPNConfig> {
            return try {
                if (serverAddress.isBlank()) {
                    return Result.failure(IllegalArgumentException("Server address is required"))
                }

                val finalRoutes = if (routingMode == RoutingMode.ALL && routes.isEmpty()) {
                    GLOBAL_ROUTES
                } else {
                    routes.toList()
                }

                val config = VPNConfig(
                    localIP = localIP,
                    prefixLength = prefixLength,
                    mtu = mtu,
                    routes = finalRoutes,
                    dnsServers = dnsServers.toList(),
                    excludedRoutes = excludedRoutes.toList(),
                    serverAddress = serverAddress,
                    serverPort = serverPort
                )

                if (!config.isValid()) {
                    return Result.failure(IllegalArgumentException("Invalid VPN configuration"))
                }

                // Log detailed VPN configuration
                logInfo("VPN configuration created successfully", mapOf(
                    "local_ip" to localIP,
                    "prefix_length" to prefixLength,
                    "mtu" to mtu,
                    "server_address" to serverAddress,
                    "server_port" to serverPort,
                    "routing_mode" to routingMode.toConfigValue(),
                    "dns_servers" to dnsServers.joinToString(","),
                    "included_routes_count" to finalRoutes.size,
                    "excluded_routes_count" to excludedRoutes.size
                ))

                // Log included routes details
                if (finalRoutes.isNotEmpty()) {
                    logDebug("VPN included routes:", mapOf(
                        "routes" to finalRoutes.map { it.toCIDR() }.joinToString(", ")
                    ))
                }

                // Log excluded routes details
                if (excludedRoutes.isNotEmpty()) {
                    logDebug("VPN excluded routes:", mapOf(
                        "routes" to excludedRoutes.map { it.toCIDR() }.joinToString(", ")
                    ))
                }

                logInfo("Built VPN configuration: ${config.toDebugString()}")
                Result.success(config)
            } catch (e: Exception) {
                logError("Failed to build VPN configuration: ${e.message}", e)
                Result.failure(e)
            }
        }
    }

    // MARK: - Interface Lifecycle Management (integrated from VPNInterfaceLifecycleManager)

    /**
     * NAME: establishInterface
     *
     * DESCRIPTION:
     *     Establishes VPN interface with provided configuration.
     *     Creates VPN tunnel, configures network settings, and manages interface lifecycle.
     *     Equivalent to iOS NEPacketTunnelNetworkSettings configuration.
     *
     * PARAMETERS:
     *     config - VPN configuration containing network settings
     *
     * RETURNS:
     *     Result<ParcelFileDescriptor> - Success with interface descriptor or failure with error
     */
    suspend fun establishInterface(config: VPNConfig): Result<ParcelFileDescriptor> =
        withContext(Dispatchers.IO) {
            try {
                logInfo("Establishing VPN interface: ${config.toDebugString()}")

                // Update state to creating
                _interfaceState.value = VPNInterfaceState.CREATING

                // Validate configuration
                if (!config.isValid()) {
                    _interfaceState.value = VPNInterfaceState.ERROR
                    return@withContext Result.failure(
                        IllegalArgumentException("Invalid VPN configuration")
                    )
                }

                // Close existing interface if active
                closeInterface()

                // Create VPN interface using integrated builder
                val builder = buildVPNInterface(config)

                // Establish VPN interface
                val newInterface = builder.establish()
                    ?: return@withContext Result.failure(
                        IOException("Failed to establish VPN interface - system denied")
                    )

                // Only update state after successful establishment
                val oldInterface = currentInterface
                currentInterface = newInterface
                currentConfig = config
                _interfaceState.value = VPNInterfaceState.ACTIVE

                // Safely close old interface after new one is established
                oldInterface?.close()

                logInfo("VPN interface established successfully", mapOf(
                    "interface_fd" to newInterface.fd,
                    "local_ip" to config.localIP,
                    "mtu" to config.mtu,
                    "routes_count" to config.routes.size
                ))
                Result.success(newInterface)

            } catch (e: SecurityException) {
                logError("VPN permission denied: ${e.message}", e)
                _interfaceState.value = VPNInterfaceState.ERROR
                cleanupOnError()
                Result.failure(SecurityException("VPN permission required: ${e.message}"))
            } catch (e: Exception) {
                logError("Failed to establish VPN interface: ${e.message}", e)
                _interfaceState.value = VPNInterfaceState.ERROR
                cleanupOnError()
                Result.failure(e)
            }
        }

    /**
     * NAME: buildVPNInterface
     *
     * DESCRIPTION:
     *     Builds VPN interface using Android VpnService.Builder.
     *     Integrates configuration building functionality from VPNConfigBuilder.
     *
     * PARAMETERS:
     *     config - VPN configuration
     *
     * RETURNS:
     *     VpnService.Builder - Configured builder ready for establishment
     */
    private fun buildVPNInterface(config: VPNConfig): VpnService.Builder {
        return vpnService.Builder().apply {
            // Set session name
            setSession("Panabit Client")

            // Set MTU
            setMtu(config.mtu)

            // Configure local IP address
            addAddress(config.localIP, config.prefixLength)
            logInfo("Configured local address: ${config.localIP}/${config.prefixLength}")

            // Configure routes
            config.routes.forEach { route ->
                try {
                    addRoute(route.destination, route.prefixLength)
                    logDebug("Added route: ${route.toCIDR()}")
                } catch (e: Exception) {
                    logWarn("Failed to add route ${route.toCIDR()}: ${e.message}")
                }
            }

            // Configure DNS servers
            config.dnsServers.forEach { dns ->
                try {
                    addDnsServer(dns)
                    logDebug("Added DNS server: $dns")
                } catch (e: Exception) {
                    logWarn("Failed to add DNS server $dns: ${e.message}")
                }
            }

            // Configure excluded routes (protected routes)
            config.excludedRoutes.forEach { route ->
                try {
                    // Note: Android doesn't have direct excluded routes API
                    // This would need to be handled at routing level
                    logDebug("Excluded route noted: ${route.toCIDR()}")
                } catch (e: Exception) {
                    logWarn("Failed to process excluded route ${route.toCIDR()}: ${e.message}")
                }
            }

            // Configure app disallowed list for socket protection optimization
            configureAppDisallowedList(this)

            // Configure blocking for non-VPN traffic (optional)
            setBlocking(true)
        }
    }

    /**
     * NAME: needsRebuild
     *
     * DESCRIPTION:
     *     Checks if VPN interface needs to be rebuilt for new configuration.
     *     Compares current and new configurations to determine rebuild necessity.
     *     Equivalent to iOS NetworkExtension configuration change detection.
     *
     * PARAMETERS:
     *     newConfig - New VPN configuration to compare
     *
     * RETURNS:
     *     Boolean - true if rebuild is needed, false otherwise
     */
    fun needsRebuild(newConfig: VPNConfig): Boolean {
        val current = currentConfig ?: return true

        // Check if critical configuration parameters have changed
        return current.localIP != newConfig.localIP ||
               current.prefixLength != newConfig.prefixLength ||
               current.mtu != newConfig.mtu ||
               current.routes != newConfig.routes ||
               current.dnsServers != newConfig.dnsServers ||
               current.excludedRoutes != newConfig.excludedRoutes
    }

    /**
     * NAME: needsNetworkRebuild
     *
     * DESCRIPTION:
     *     Checks if interface needs rebuild due to network changes.
     *     Used for network switching scenarios (WiFi <-> Mobile).
     *
     * PARAMETERS:
     *     network - New network instance (can be null)
     *
     * RETURNS:
     *     Boolean - true if rebuild is needed for network change
     */
    fun needsNetworkRebuild(network: android.net.Network?): Boolean {
        // Android VPN interfaces typically don't need rebuild for network changes
        // The system handles network switching automatically
        // Only rebuild if interface is not active or configuration is invalid
        return _interfaceState.value != VPNInterfaceState.ACTIVE || currentConfig == null
    }

    /**
     * NAME: rebuildForNetwork
     *
     * DESCRIPTION:
     *     Rebuilds VPN interface for network change scenario.
     *     Maintains current configuration while adapting to new network.
     *
     * PARAMETERS:
     *     config - Current VPN configuration
     *     network - New network instance
     *
     * RETURNS:
     *     Result<ParcelFileDescriptor> - Success with new interface or failure
     */
    suspend fun rebuildForNetwork(
        config: VPNConfig,
        network: android.net.Network?
    ): Result<ParcelFileDescriptor> = withContext(Dispatchers.IO) {
        try {
            Log.i(TAG, "Rebuilding VPN interface for network change")

            // Bind to specific network if provided
            network?.let {
                vpnService.setUnderlyingNetworks(arrayOf(it))
                Log.d(TAG, "Bound VPN to network: $it")
            }

            // Re-establish interface with same configuration
            establishInterface(config)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to rebuild interface for network", e)
            Result.failure(e)
        }
    }

    /**
     * NAME: getCurrentConfig
     *
     * DESCRIPTION:
     *     Gets current VPN configuration.
     *
     * RETURNS:
     *     VPNConfig? - Current configuration or null if not active
     */
    fun getCurrentConfig(): VPNConfig? = currentConfig

    /**
     * NAME: isActive
     *
     * DESCRIPTION:
     *     Checks if VPN interface is currently active.
     *
     * RETURNS:
     *     Boolean - true if interface is active, false otherwise
     */
    fun isActive(): Boolean = synchronized(this) {
        _interfaceState.value == VPNInterfaceState.ACTIVE && currentInterface != null
    }

    /**
     * NAME: closeInterface
     *
     * DESCRIPTION:
     *     Closes current VPN interface and cleans up resources.
     *     Equivalent to iOS NetworkExtension tunnel stop.
     */
    fun closeInterface() {
        val closeStartTime = System.currentTimeMillis()
        logInfo("🔒 [VPN-INTERFACE-PERF] Starting VPN interface close", mapOf(
            "start_time" to closeStartTime,
            "current_state" to _interfaceState.value.name,
            "has_interface" to (currentInterface != null)
        ))

        val syncStartTime = System.currentTimeMillis()
        synchronized(this) {
            val syncAcquiredTime = System.currentTimeMillis()
            val syncWaitDuration = syncAcquiredTime - syncStartTime

            if (syncWaitDuration > 10) { // Log if sync wait > 10ms
                logInfo("🔒 [VPN-INTERFACE-PERF] Synchronized block acquired", mapOf(
                    "sync_wait_ms" to syncWaitDuration
                ))
            }

            try {
                val stateUpdateStartTime = System.currentTimeMillis()
                _interfaceState.value = VPNInterfaceState.DESTROYING
                val stateUpdateDuration = System.currentTimeMillis() - stateUpdateStartTime

                val interfaceCloseStartTime = System.currentTimeMillis()
                currentInterface?.close()
                val interfaceCloseDuration = System.currentTimeMillis() - interfaceCloseStartTime

                val totalDuration = System.currentTimeMillis() - closeStartTime
                logInfo("✅ [VPN-INTERFACE-PERF] VPN interface closed successfully", mapOf(
                    "total_duration_ms" to totalDuration,
                    "sync_wait_ms" to syncWaitDuration,
                    "state_update_ms" to stateUpdateDuration,
                    "interface_close_ms" to interfaceCloseDuration
                ))
            } catch (e: Exception) {
                val totalDuration = System.currentTimeMillis() - closeStartTime
                logWarn("⚠️ [VPN-INTERFACE-PERF] Error closing VPN interface", mapOf(
                    "error" to (e.message ?: "unknown"),
                    "total_duration_ms" to totalDuration
                ), e)
            } finally {
                val cleanupStartTime = System.currentTimeMillis()
                currentInterface = null
                currentConfig = null
                resetAppDisallowedStatus()  // Reset app disallowed status for next session
                _interfaceState.value = VPNInterfaceState.INACTIVE
                val cleanupDuration = System.currentTimeMillis() - cleanupStartTime

                val totalDuration = System.currentTimeMillis() - closeStartTime
                logInfo("🧹 [VPN-INTERFACE-PERF] VPN interface cleanup completed", mapOf(
                    "cleanup_duration_ms" to cleanupDuration,
                    "total_duration_ms" to totalDuration,
                    "app_disallowed_status_reset" to true
                ))
            }
        }
    }

    /**
     * NAME: cleanupOnError
     *
     * DESCRIPTION:
     *     Cleans up resources when an error occurs during interface establishment.
     *     Ensures no resource leaks in error scenarios.
     */
    private fun cleanupOnError() {
        synchronized(this) {
            try {
                currentInterface?.close()
            } catch (e: Exception) {
                logWarn("Error during cleanup: ${e.message}", e)
            } finally {
                currentInterface = null
                currentConfig = null
                resetAppDisallowedStatus()  // Reset app disallowed status on error
                _interfaceState.value = VPNInterfaceState.ERROR
                logDebug("Cleanup on error completed with app disallowed status reset")
            }
        }
    }

    /**
     * NAME: configureRoutes
     *
     * DESCRIPTION:
     *     Configures routing rules for VPN interface with exclusion support.
     *     Handles both global routing and custom routing modes.
     *     Implements route exclusion by splitting global routes around excluded networks.
     *
     * PARAMETERS:
     *     builder - VpnService.Builder instance
     *     config - VPN configuration with routing information
     */
    private fun configureRoutes(builder: VpnService.Builder, config: VPNConfig) {
        logInfo("Configuring VPN routes", mapOf(
            "included_routes" to config.routes.size,
            "excluded_routes" to config.excludedRoutes.size
        ))

        if (config.routes.isEmpty()) {
            // Default: route all traffic through VPN (exclusions handled separately)
            builder.addRoute("0.0.0.0", 0)
            logDebug("Added default route (all traffic)")
        } else {
            // Custom routing: add specified routes (exclusions don't apply to custom routes)
            config.routes.forEach { route ->
                if (route.isValid()) {
                    builder.addRoute(route.destination, route.prefixLength)
                    logDebug("Added custom route: ${route.toCIDR()}")
                }
            }
            logInfo("Applied custom routes", mapOf("routes_count" to config.routes.size))
        }
    }

    /**
     * NAME: configureDNS
     *
     * DESCRIPTION:
     *     Configures DNS servers for VPN interface.
     *
     * PARAMETERS:
     *     builder - VpnService.Builder instance
     *     config - VPN configuration with DNS settings
     */
    private fun configureDNS(builder: VpnService.Builder, config: VPNConfig) {
        config.dnsServers.forEach { dnsServer ->
            try {
                builder.addDnsServer(InetAddress.getByName(dnsServer))
                Log.d(TAG, "Added DNS server: $dnsServer")
            } catch (e: Exception) {
                Log.w(TAG, "Invalid DNS server: $dnsServer", e)
            }
        }
    }



    /**
     * NAME: configureExcludedRoutes
     *
     * DESCRIPTION:
     *     Configures excluded routes using Android API.
     *     Uses excludeRoute() on API 33+ or socket protection on older versions.
     *
     * PARAMETERS:
     *     builder - VpnService.Builder instance
     *     config - VPN configuration with excluded routes
     */
    private fun configureExcludedRoutes(builder: VpnService.Builder, config: VPNConfig) {
        if (config.excludedRoutes.isEmpty()) {
            logDebug("No excluded routes to configure")
            return
        }

        val apiLevel = Build.VERSION.SDK_INT
        var excludedCount = 0
        var skippedCount = 0

        config.excludedRoutes.forEach { route ->
            if (!route.isValid()) {
                logWarn("Invalid excluded route skipped: ${route.toCIDR()}")
                skippedCount++
                return@forEach
            }

            try {
                if (apiLevel >= Build.VERSION_CODES.TIRAMISU) { // API 33+
                    // Use official excludeRoute API
                    val ipPrefix = IpPrefix(
                        InetAddress.getByName(route.destination),
                        route.prefixLength
                    )
                    builder.excludeRoute(ipPrefix)
                    logDebug("Excluded route via API: ${route.toCIDR()}")
                    excludedCount++
                } else {
                    // API 32 and below: Log for socket protection fallback
                    logDebug("Excluded route (socket protection): ${route.toCIDR()}")
                    excludedCount++
                }
            } catch (e: Exception) {
                logWarn("Failed to exclude route ${route.toCIDR()}: ${e.message}")
                skippedCount++
            }
        }

        logInfo("Configured excluded routes", mapOf(
            "api_level" to apiLevel,
            "total_excluded" to config.excludedRoutes.size,
            "successfully_excluded" to excludedCount,
            "skipped" to skippedCount,
            "server_exclusions" to config.excludedRoutes.count { it.prefixLength == 32 },
            "network_exclusions" to config.excludedRoutes.count { it.prefixLength < 32 },
            "method" to if (apiLevel >= Build.VERSION_CODES.TIRAMISU) "excludeRoute_API" else "socket_protection"
        ))
    }
}

/**
 * NAME: VPNConfigTemplates
 *
 * DESCRIPTION:
 *     Predefined VPN configuration templates for common use cases.
 *     Provides convenient factory methods for typical VPN configurations.
 *     Integrated with VPNInterfaceManager for streamlined usage.
 */
object VPNConfigTemplates {

    /**
     * NAME: globalRouting
     *
     * DESCRIPTION:
     *     Creates VPN configuration template for global routing (route all traffic).
     *     Includes standard DNS servers, static local network exclusions, and dynamic local subnet exclusion.
     *
     * PARAMETERS:
     *     manager - VPNInterfaceManager instance
     *     context - Android context for network interface detection
     *     serverAddress - VPN server address
     *     serverPort - VPN server port (default: 443)
     *
     * RETURNS:
     *     VPNInterfaceManager.ConfigBuilder - Pre-configured builder for global routing
     */
    fun globalRouting(
        manager: VPNInterfaceManager,
        context: Context,
        serverAddress: String,
        serverPort: Int = 443
    ): VPNInterfaceManager.ConfigBuilder {
        return manager.createConfigBuilder()
            .setServerAddress(serverAddress)
            .setServerPort(serverPort)
            .useGlobalRouting()
            .setDNSServers(listOf("*******", "*******"))
            .addLocalNetworkExclusions()
            .addDynamicLocalSubnetExclusion(context)
    }

    /**
     * NAME: customRouting
     *
     * DESCRIPTION:
     *     Creates VPN configuration template for custom routing (split tunneling).
     *
     * PARAMETERS:
     *     serverAddress - VPN server address
     *     routes - Custom routes to tunnel through VPN
     *     serverPort - VPN server port (default: 443)
     *
     * RETURNS:
     *     VPNInterfaceManager.ConfigBuilder - Pre-configured builder for custom routing
     */
    fun customRouting(
        manager: VPNInterfaceManager,
        serverAddress: String,
        routes: List<RouteInfo>,
        serverPort: Int = 443
    ): VPNInterfaceManager.ConfigBuilder {
        return manager.createConfigBuilder()
            .setServerAddress(serverAddress)
            .setServerPort(serverPort)
            .setRoutingMode(RoutingMode.CUSTOM)
            .apply {
                routes.forEach { route ->
                    addRoute(route.destination, route.prefixLength)
                }
            }
            .setDNSServers(listOf("*******", "*******"))
    }

    /**
     * NAME: enterpriseConfig
     *
     * DESCRIPTION:
     *     Creates VPN configuration template optimized for enterprise use.
     *     Includes dynamic local subnet exclusion for corporate network compatibility.
     *
     * PARAMETERS:
     *     manager - VPNInterfaceManager instance
     *     context - Android context for network interface detection
     *     serverAddress - VPN server address
     *     serverPort - VPN server port (default: 443)
     *     corporateDNS - Corporate DNS servers (optional)
     *
     * RETURNS:
     *     VPNInterfaceManager.ConfigBuilder - Pre-configured builder for enterprise use
     */
    fun enterpriseConfig(
        manager: VPNInterfaceManager,
        context: Context,
        serverAddress: String,
        serverPort: Int = 443,
        corporateDNS: List<String> = emptyList()
    ): VPNInterfaceManager.ConfigBuilder {
        val dnsServers = if (corporateDNS.isNotEmpty()) {
            corporateDNS
        } else {
            listOf("*******", "*******")
        }

        return manager.createConfigBuilder()
            .setServerAddress(serverAddress)
            .setServerPort(serverPort)
            .setMTU(1400)  // Conservative MTU for enterprise networks
            .useGlobalRouting()
            .setDNSServers(dnsServers)
            .addLocalNetworkExclusions()
            .addDynamicLocalSubnetExclusion(context)
    }
}


    // MARK: - App Disallowed Status Management

    /**
     * NAME: configureAppDisallowedList
     *
     * DESCRIPTION:
     *     Configures current application in VPN disallowed list to prevent its traffic
     *     from entering VPN tunnel. This optimization eliminates the need for frequent
     *     protect() calls on UDP sockets during VPN operations.
     *
     *     Called during every VPN interface creation (initial, reconnect, rebuild).
     *
     * PARAMETERS:
     *     builder - VpnService.Builder instance to configure
     */
    private fun configureAppDisallowedList(builder: VpnService.Builder) {
        try {
            val packageName = vpnService.packageName
            builder.addDisallowedApplication(packageName)
            isCurrentAppDisallowed = true  // Track successful disallowance
            logInfo("Added current app to VPN disallowed list", mapOf(
                "package_name" to packageName,
                "benefit" to "eliminates_need_for_socket_protect_calls",
                "optimization_enabled" to true,
                "context" to "vpn_interface_creation"
            ))
        } catch (e: Exception) {
            isCurrentAppDisallowed = false  // Track failed disallowance
            logWarn("Failed to add current app to disallowed list, will fallback to socket protection", mapOf(
                "error" to (e.message ?: "unknown"),
                "fallback" to "socket_protect_method",
                "optimization_enabled" to false,
                "context" to "vpn_interface_creation"
            ))
        }
    }

    /**
     * NAME: isCurrentAppDisallowed
     *
     * DESCRIPTION:
     *     Checks if current application is in VPN disallowed list.
     *     Used by VPN service to optimize socket protection by skipping protect() calls.
     *
     * RETURNS:
     *     Boolean - true if app is in disallowed list, false otherwise
     */
    fun isCurrentAppDisallowed(): Boolean {
        return isCurrentAppDisallowed
    }

    /**
     * NAME: resetAppDisallowedStatus
     *
     * DESCRIPTION:
     *     Resets app disallowed status when VPN interface is destroyed.
     *     Called during interface cleanup to ensure correct state tracking.
     */
    private fun resetAppDisallowedStatus() {
        isCurrentAppDisallowed = false
        logDebug("Reset app disallowed status for next VPN session")
    }
}
